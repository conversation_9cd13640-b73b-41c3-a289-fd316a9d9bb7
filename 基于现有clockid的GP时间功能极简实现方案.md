# 基于现有clockid的GP时间功能极简实现方案

## 1. 现有clockid_t分析

### 1.1 Trusty TEE现有时钟类型

```c
#define CLOCK_REALTIME           0  // 实时时钟 - 可用于GP系统时间
#define CLOCK_MONOTONIC          1  // 单调时钟 - 可用于GP系统时间
#define CLOCK_PROCESS_CPUTIME_ID 2
#define CLOCK_THREAD_CPUTIME_ID  3
#define CLOCK_MONOTONIC_RAW      4
#define CLOCK_REALTIME_COARSE    5
#define CLOCK_MONOTONIC_COARSE   6
#define CLOCK_BOOTTIME           7  // 启动时间 - 可用于GP REE时间
#define CLOCK_REALTIME_ALARM     8
#define CLOCK_BOOTTIME_ALARM     9
#define CLOCK_SGI_CYCLE         10
#define CLOCK_TAI               11  // TAI时间 - 可用于GP系统时间
```

### 1.2 现有sys_gettime实现分析

```c
long sys_gettime(uint32_t clock_id, uint32_t flags, user_addr_t time) {
#if USE_IMX_MONOTONIC_TIME
    if (clock_id == CLOCK_MONOTONIC) {
        int64_t monotonic_t_64 = (int64_t)monotonic_time_s();
        return copy_to_user(time, &monotonic_t_64, sizeof(int64_t));
    }
#endif
    // return time in nanoseconds
    lk_time_ns_t t = current_time_ns();
    return copy_to_user(time, &t, sizeof(int64_t));
}
```

**关键发现：**
- `CLOCK_REALTIME` 和 `CLOCK_MONOTONIC` 都返回 `current_time_ns()` - **可直接用于GP系统时间**
- `CLOCK_BOOTTIME` 也返回 `current_time_ns()` - **可直接用于GP REE时间**
- 现有实现已经提供了纳秒级精度的时间戳

## 2. GP标准时间映射方案

### 2.1 直接映射现有clockid_t

**无需新增任何clockid_t定义！**

```c
/* GP标准时间到现有clockid_t的直接映射 */
#define GP_SYSTEM_TIME_CLOCK    CLOCK_REALTIME     // TEE_GetSystemTime()
#define GP_REE_TIME_CLOCK       CLOCK_BOOTTIME     // TEE_GetREETime()
// TA持久时间需要特殊处理，使用保留的clockid_t值
#define GP_TA_PERSISTENT_CLOCK  100                // TEE_GetTAPersistentTime()
```

### 2.2 时间格式转换

```c
/* 纳秒时间戳到TEE_Time的转换 */
static inline void ns_to_tee_time(int64_t time_ns, TEE_Time* tee_time) {
    // 需要转换为Unix时间戳（加上启动时偏移量）
    static int64_t boot_time_offset = 1640995200000000000LL; // 示例
    int64_t unix_time_ns = time_ns + boot_time_offset;
    
    tee_time->seconds = (uint32_t)(unix_time_ns / 1000000000LL);
    tee_time->millis = (uint32_t)((unix_time_ns % 1000000000LL) / 1000000LL);
}

/* TEE_Time到纳秒时间戳的转换 */
static inline int64_t tee_time_to_ns(const TEE_Time* tee_time) {
    return (int64_t)tee_time->seconds * 1000000000LL + 
           (int64_t)tee_time->millis * 1000000LL;
}
```

## 3. 极简实现方案

### 3.1 GP API层实现 (user/base/lib/libutee/tee_time.c)

```c
#include <tee_internal_api.h>
#include <time.h>
#include <rctee/time.h>

/* GP标准时间到现有clockid_t映射 */
#define GP_SYSTEM_TIME_CLOCK    CLOCK_REALTIME
#define GP_REE_TIME_CLOCK       CLOCK_BOOTTIME
#define GP_TA_PERSISTENT_CLOCK  100

/*
 * TEE_GetSystemTime - 获取系统时间
 * 直接使用现有CLOCK_REALTIME
 */
void TEE_GetSystemTime(TEE_Time* time) {
    if (!time) {
        panic("TEE_GetSystemTime: null pointer");
    }
    
    int64_t time_ns;
    int rc = rctee_gettime(GP_SYSTEM_TIME_CLOCK, &time_ns);
    if (rc != 0) {
        panic("TEE_GetSystemTime: failed to get system time");
    }
    
    ns_to_tee_time(time_ns, time);
}

/*
 * TEE_GetREETime - 获取REE时间
 * 直接使用现有CLOCK_BOOTTIME
 */
void TEE_GetREETime(TEE_Time* time) {
    if (!time) {
        panic("TEE_GetREETime: null pointer");
    }
    
    int64_t time_ns;
    int rc = rctee_gettime(GP_REE_TIME_CLOCK, &time_ns);
    if (rc != 0) {
        panic("TEE_GetREETime: failed to get REE time");
    }
    
    ns_to_tee_time(time_ns, time);
}

/*
 * TEE_GetTAPersistentTime - 获取TA持久时间
 * 使用偏移量机制
 */
TEE_Result TEE_GetTAPersistentTime(TEE_Time* time) {
    if (!time) {
        return TEE_ERROR_BAD_PARAMETERS;
    }
    
    /* 尝试使用特殊clockid获取TA持久时间 */
    int64_t time_ns;
    int rc = rctee_gettime(GP_TA_PERSISTENT_CLOCK, &time_ns);
    
    if (rc == 0) {
        ns_to_tee_time(time_ns, time);
        return TEE_SUCCESS;
    }
    
    /* 处理错误情况 */
    memset(time, 0, sizeof(TEE_Time));
    switch (-rc) {
        case ENODATA:
            return TEE_ERROR_TIME_NOT_SET;
        case ESTALE:
            return TEE_ERROR_TIME_NEEDS_RESET;
        default:
            return TEE_ERROR_GENERIC;
    }
}

/*
 * TEE_SetTAPersistentTime - 设置TA持久时间
 * 调用专门的设置接口
 */
TEE_Result TEE_SetTAPersistentTime(const TEE_Time* time) {
    if (!time || time->millis >= 1000) {
        return TEE_ERROR_BAD_PARAMETERS;
    }
    
    /* 调用专门的TA持久时间设置接口 */
    int rc = rctee_set_ta_persistent_time(time);
    return (rc == 0) ? TEE_SUCCESS : TEE_ERROR_GENERIC;
}

/*
 * TEE_Wait - 时间等待
 * 直接使用现有rctee_nanosleep
 */
TEE_Result TEE_Wait(uint32_t timeout) {
    if (timeout == TEE_TIMEOUT_INFINITE) {
        timeout = UINT32_MAX - 1;
    }
    
    uint64_t sleep_ns = (uint64_t)timeout * 1000000ULL;
    int rc = rctee_nanosleep(CLOCK_MONOTONIC, 0, sleep_ns);
    return (rc == 0) ? TEE_SUCCESS : TEE_ERROR_GENERIC;
}
```

### 3.2 系统调用层最小修改

#### **3.2.1 扩展sys_gettime处理TA持久时间**

在`kernel/rctee/lib/rctee/rctee_core/syscall.c`中只需要添加一个分支：

```c
long sys_gettime(uint32_t clock_id, uint32_t flags, user_addr_t time) {
    /* 处理TA持久时间的特殊clockid */
    if (clock_id == GP_TA_PERSISTENT_CLOCK) {
        return sys_gettime_ta_persistent(flags, time);
    }
    
    /* 现有逻辑完全保持不变 */
#if USE_IMX_MONOTONIC_TIME
    if (clock_id == CLOCK_MONOTONIC) {
        int64_t monotonic_t_64 = (int64_t)monotonic_time_s();
        return copy_to_user(time, &monotonic_t_64, sizeof(int64_t));
    }
#endif
    // return time in nanoseconds
    lk_time_ns_t t = current_time_ns();
    return copy_to_user(time, &t, sizeof(int64_t));
}

/* 新增TA持久时间处理函数 */
static long sys_gettime_ta_persistent(uint32_t flags, user_addr_t time) {
    TEE_Time tee_time;
    TEE_Result res = get_ta_persistent_time_internal(&tee_time);
    
    switch (res) {
        case TEE_SUCCESS: {
            int64_t time_ns = tee_time_to_ns(&tee_time);
            return copy_to_user(time, &time_ns, sizeof(int64_t));
        }
        case TEE_ERROR_TIME_NOT_SET:
            return -ENODATA;
        case TEE_ERROR_TIME_NEEDS_RESET:
            return -ESTALE;
        default:
            return -EIO;
    }
}
```

#### **3.2.2 新增TA持久时间设置接口**

在`user/base/lib/libc-rctee/time.c`中添加：

```c
/* 新增TA持久时间设置接口 */
int rctee_set_ta_persistent_time(const TEE_Time* time) {
    return _rctee_syscall(__NR_set_ta_persistent_time, (user_addr_t)time);
}
```

## 4. 文件修改清单

### 4.1 需要修改的文件（最小化）

#### **4.1.1 user/base/lib/libutee/include/tee_internal_api.h** (新增约30行)
```c
/* 新增TEE_Time结构体和GP错误码定义 */
/* 新增GP时间API函数声明 */
/* 新增时间转换内联函数 */
```

#### **4.1.2 user/base/lib/libc-rctee/time.c** (新增约10行)
```c
/* 新增rctee_set_ta_persistent_time()函数 */
/* 新增相关头文件包含 */
```

#### **4.1.3 kernel/rctee/lib/rctee/rctee_core/syscall.c** (新增约30行)
```c
/* 在sys_gettime()中添加一个if分支处理GP_TA_PERSISTENT_CLOCK */
/* 新增sys_gettime_ta_persistent()静态函数 */
/* 新增sys_set_ta_persistent_time()函数 */
```

### 4.2 需要新增的文件

#### **4.2.1 user/base/lib/libutee/tee_time.c** (新增约100行)
```c
/* 实现所有5个GP时间API */
/* 基于现有clockid_t的直接映射实现 */
```

## 5. 实现优势

### 5.1 极简设计

- **无需新增clockid_t定义**：直接复用现有CLOCK_REALTIME、CLOCK_BOOTTIME
- **最小系统调用修改**：仅在sys_gettime中添加一个分支
- **总代码量**：约170行（比之前方案减少40%）
- **修改文件**：仅3个现有文件的微小修改

### 5.2 完美兼容

- **100%复用现有时间基础设施**：TEE_GetSystemTime和TEE_GetREETime直接使用现有clockid
- **零破坏性修改**：现有所有时间API功能完全不受影响
- **调用链路一致**：完全基于现有rctee_gettime()机制

### 5.3 性能最优

- **零额外开销**：TEE_GetSystemTime和TEE_GetREETime性能与现有时间API完全相同
- **直接映射**：无需额外的时间格式转换或计算
- **缓存友好**：复用现有时间获取路径

### 5.4 维护简单

- **代码复杂度最低**：基于简单的clockid映射
- **测试验证简单**：主要测试TA持久时间功能
- **文档清晰**：映射关系一目了然

## 6. 总结

这个基于现有clockid的极简实现方案是最优解：

1. **TEE_GetSystemTime** → 直接使用 `CLOCK_REALTIME`
2. **TEE_GetREETime** → 直接使用 `CLOCK_BOOTTIME`  
3. **TEE_Wait** → 直接使用现有 `rctee_nanosleep()`
4. **TA持久时间** → 仅需要特殊处理，使用偏移量机制

这种方案最大化利用了Trusty TEE现有的时间管理基础设施，实现了GP标准时间功能的完整支持，同时保持了代码的最大简洁性。
